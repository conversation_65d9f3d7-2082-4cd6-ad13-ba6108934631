/**
 * Integration tests for SignupModal component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SignupModal } from '../components/signup-modal';
import { SignupModalProvider } from '../contexts/signup-modal-context';

// Mock the API module
jest.mock('../lib/api', () => ({
  submitForm: jest.fn(),
  mapUserTypeToApiFormat: jest.fn((type) => type === 'member' ? 'member' : 'verifier')
}));

// Mock the honeypot hook
jest.mock('../hooks/use-honeypot', () => ({
  useHoneypot: () => ({
    honeypotValue: '',
    setHoneypotValue: jest.fn(),
    validateSubmission: () => ({ isValid: true }),
    resetHoneypot: jest.fn()
  })
}));

const MockSignupModalProvider = ({ children, isOpen = true, userType = 'member' }: any) => {
  const mockContext = {
    isOpen,
    userType,
    openModal: jest.fn(),
    closeModal: jest.fn()
  };

  return (
    <SignupModalProvider value={mockContext}>
      {children}
    </SignupModalProvider>
  );
};

describe('SignupModal Form Validation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should display validation errors for empty fields on submit', async () => {
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    const submitButton = screen.getByRole('button', { name: /submit signup form/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/linkedin.*required/i)).toBeInTheDocument();
    });
  });

  it('should display validation errors for invalid email format', async () => {
    const user = userEvent.setup();
    
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    const emailInput = screen.getByPlaceholderText(/email address/i);
    await user.type(emailInput, 'invalid-email');
    await user.tab(); // Trigger onBlur

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('should display validation errors for invalid LinkedIn URL', async () => {
    const user = userEvent.setup();
    
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    const linkedinInput = screen.getByPlaceholderText(/linkedin profile url/i);
    await user.type(linkedinInput, 'invalid-url');
    await user.tab(); // Trigger onBlur

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid linkedin profile url/i)).toBeInTheDocument();
    });
  });

  it('should clear validation errors when user starts typing', async () => {
    const user = userEvent.setup();
    
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    // First trigger validation error
    const emailInput = screen.getByPlaceholderText(/email address/i);
    await user.type(emailInput, 'invalid');
    await user.tab();

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });

    // Then start typing to clear error
    await user.clear(emailInput);
    await user.type(emailInput, 'test@');

    await waitFor(() => {
      expect(screen.queryByText(/please enter a valid email address/i)).not.toBeInTheDocument();
    });
  });

  it('should prevent form submission when validation fails', async () => {
    const mockSubmitForm = require('../lib/api').submitForm;
    
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    const submitButton = screen.getByRole('button', { name: /submit signup form/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    });

    // Ensure API was not called
    expect(mockSubmitForm).not.toHaveBeenCalled();
  });

  it('should submit form with valid data', async () => {
    const mockSubmitForm = require('../lib/api').submitForm;
    mockSubmitForm.mockResolvedValue({ success: true });

    const user = userEvent.setup();
    
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    const emailInput = screen.getByPlaceholderText(/email address/i);
    const linkedinInput = screen.getByPlaceholderText(/linkedin profile url/i);
    const submitButton = screen.getByRole('button', { name: /submit signup form/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(linkedinInput, 'https://linkedin.com/in/johndoe');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockSubmitForm).toHaveBeenCalledWith({
        email: '<EMAIL>',
        fields: {
          linkedin: 'https://linkedin.com/in/johndoe',
          user_type: 'member',
          source: 'signup-modal',
          website: '',
          form_start_time: expect.any(String)
        }
      });
    });
  });

  it('should display success message after successful submission', async () => {
    const mockSubmitForm = require('../lib/api').submitForm;
    mockSubmitForm.mockResolvedValue({ success: true });

    const user = userEvent.setup();
    
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    const emailInput = screen.getByPlaceholderText(/email address/i);
    const linkedinInput = screen.getByPlaceholderText(/linkedin profile url/i);
    const submitButton = screen.getByRole('button', { name: /submit signup form/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(linkedinInput, 'https://linkedin.com/in/johndoe');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/success.*waitlist/i)).toBeInTheDocument();
    });
  });

  it('should display error message when submission fails', async () => {
    const mockSubmitForm = require('../lib/api').submitForm;
    mockSubmitForm.mockResolvedValue({ 
      success: false, 
      error: 'This email is already subscribed.' 
    });

    const user = userEvent.setup();
    
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    const emailInput = screen.getByPlaceholderText(/email address/i);
    const linkedinInput = screen.getByPlaceholderText(/linkedin profile url/i);
    const submitButton = screen.getByRole('button', { name: /submit signup form/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(linkedinInput, 'https://linkedin.com/in/johndoe');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/this email is already subscribed/i)).toBeInTheDocument();
    });
  });
});

describe('SignupModal Accessibility', () => {
  it('should have proper ARIA attributes for error messages', async () => {
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    const submitButton = screen.getByRole('button', { name: /submit signup form/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      const errorMessages = screen.getAllByRole('alert');
      expect(errorMessages.length).toBeGreaterThan(0);
      
      errorMessages.forEach(error => {
        expect(error).toHaveAttribute('aria-live', 'polite');
      });
    });
  });

  it('should associate error messages with form fields', async () => {
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    const emailInput = screen.getByPlaceholderText(/email address/i);
    const linkedinInput = screen.getByPlaceholderText(/linkedin profile url/i);

    expect(emailInput).toHaveAttribute('aria-describedby', 'email-error');
    expect(linkedinInput).toHaveAttribute('aria-describedby', 'linkedin-error');
  });

  it('should mark invalid fields with aria-invalid', async () => {
    const user = userEvent.setup();
    
    render(
      <MockSignupModalProvider>
        <SignupModal />
      </MockSignupModalProvider>
    );

    const emailInput = screen.getByPlaceholderText(/email address/i);
    await user.type(emailInput, 'invalid');
    await user.tab();

    await waitFor(() => {
      expect(emailInput).toHaveAttribute('aria-invalid', 'true');
    });
  });
});
